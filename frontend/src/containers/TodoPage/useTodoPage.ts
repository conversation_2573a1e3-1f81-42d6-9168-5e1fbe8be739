import { useQueryState, parseAsInteger } from 'nuqs';
import { ITodoDto } from '@/api/todos/types';
import { useGetTodos, useGetTodosSuggested, useUpdateTodo } from '@/hooks/useTodoService';
import { toNaiveISOString } from '@/lib/toNaiveISOString';
import { useMemo, useState } from 'react';
import { mutate } from 'swr';
import { API_ENDPOINTS, getApiUrl } from '@/constants/api';
import { useTodoContext } from './useTodoContext';
import { toast } from '@/components/ui/sonner';
import { useAuth } from '@clerk/nextjs';

const VISIBLE_SUGGETSED_TODOS_LIMIT = 5;

export const useTodoPage = () => {
  const { isSignedIn } = useAuth();

  const [removingIds, setRemovingIds] = useState<number[]>([]);
  const [selectedTodoId, setSelectedTodoId] = useQueryState<ITodoDto['id']>('todo', parseAsInteger);
  const [_, setSelectedChatId] = useQueryState<number>('chatId', parseAsInteger);
  const { data: todos = [], isLoading: isTodosLoading } = useGetTodos(isSignedIn);
  const { data: todosSuggested = [], isLoading: isTodosSuggestedLoading } =
    useGetTodosSuggested(isSignedIn);
  const { trigger: markTodoAsDone } = useUpdateTodo();
  const { confirmAction } = useTodoContext();

  const handleChecked = async (id: ITodoDto['id']) => {
    try {
      await confirmAction('markAsDone', null);
      // do nothing, the user cancelled the action
    } catch (_err) {
      const url = getApiUrl(API_ENDPOINTS.TODOS_ACCEPTED);
      const optimisticDoneDate = toNaiveISOString(new Date());

      mutate(
        url,
        async (currentTodos: ITodoDto[] = []) => {
          const todo = currentTodos.find((todo) => todo.id === id);

          if (!todo) {
            return;
          }

          await markTodoAsDone({
            todoId: id,
            data: { ...todo, doneDate: optimisticDoneDate },
          });

          return currentTodos.map((todo) =>
            todo.id === id ? { ...todo, doneDate: optimisticDoneDate } : todo
          );
        },
        {
          optimisticData: todos.map((todo) =>
            todo.id === id ? { ...todo, doneDate: optimisticDoneDate } : todo
          ),
          rollbackOnError: true,
          populateCache: true,
          revalidate: false,
        }
      );

      setRemovingIds((prev) => [...prev, id]);
      setTimeout(() => {
        setRemovingIds((prev) => prev.filter((removingId) => removingId !== id));
        toast.success({ title: `${todos.find((todo) => todo.id === id)?.name} done` });
      }, 800);
    }
  };

  const handleCloseTodoDetails = () => {
    setSelectedTodoId(null);
    setSelectedChatId(null);
  };

  const handleClickTodo = (id: number) => {
    setSelectedTodoId(id);
  };

  const filteredTodos = useMemo(
    () => todos.filter((todo) => !todo.doneDate || removingIds.includes(todo.id)),
    [todos, removingIds]
  );

  const shouldShowEmptyState = useMemo(() => {
    return (
      filteredTodos.length === 0 &&
      todosSuggested.length === 0 &&
      !isTodosLoading &&
      !isTodosSuggestedLoading
    );
  }, [filteredTodos.length, isTodosLoading]);

  return {
    todos: filteredTodos,
    todosSuggested: todosSuggested.slice(0, VISIBLE_SUGGETSED_TODOS_LIMIT),
    removingIds,
    selectedTodoId,
    markTodoAsDone,
    isTodosLoading,
    shouldShowEmptyState,
    handleChecked,
    handleCloseTodoDetails,
    handleClickTodo,
  };
};
