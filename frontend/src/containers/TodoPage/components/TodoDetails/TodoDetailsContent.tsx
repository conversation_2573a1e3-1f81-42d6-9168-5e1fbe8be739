'use client';

import { ITodoDto } from '@/api/todos/types';
import { Button } from '@/components/ui/button';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import { IconSvgObject } from '@hugeicons/react';
import { Calendar04Icon, LinkSquare01Icon } from '@hugeicons-pro/core-stroke-standard';
import Image from 'next/image';
import { Spinner } from '@/components/Spinner';
import { format } from 'date-fns';
import { Composer } from '@/components/Composer';
import { google } from 'calendar-link';
import { ApprovalActions } from '../Todo/actions/ApprovalActions';
import { ChatLinkedEntityInfo } from '@/api/entityLink';
import { Message } from '@/components/Message';
import { cn } from '@/lib/utils';

interface ITodoDetailsContentProps {
  todo?: ITodoDto | null;
  chatMessages?: ChatLinkedEntityInfo;
  onClose?: () => void;
}

export const TodoDetailsContent = ({ todo, chatMessages, onClose }: ITodoDetailsContentProps) => {
  if (!todo) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <Spinner color="var(--colors-yellow-500)" size={100} />
      </div>
    );
  }

  const description = `
  ${todo.description ?? ''}
  
  📲 View in the Hey Alfie app: 
  ${window && window.location.href}
  `;

  const googleCalendarLink = google({
    title: todo.name,
    description,
    start: todo.dueDate ?? new Date(),
    allDay: true,
  });

  return (
    <div
      className={cn('w-full overflow-x-hidden overflow-y-auto', { 'pb-[180px]': !chatMessages })}
    >
      <div className="flex flex-col gap-3 mb-4 justify-start pt-5 items-start">
        <p className="text-lg font-[Quasimoda] leading-[27px]">{todo.name}</p>
        {todo.type === 'systemCreated' && (
          <p className="text-xs font-[Quasimoda] leading-[21px] text-yellow-600 bg-yellow-50 border border-yellow-500 px-[5px] py-0.5 rounded-full">
            Suggested by Alfie
          </p>
        )}
        <p className="text-sm font-[Quasimoda] leading-[21px] text-gray-600 flex items-center gap-1">
          <HugeiconsIcon icon={Calendar04Icon as unknown as IconSvgObject} size={17} />
          Due by {todo.dueDate ? format(todo.dueDate, 'dd/MM/yyyy') : 'not provided'}
        </p>
        {todo.type !== 'systemCreated' && todo.dueDate && (
          <Button
            size="xs"
            variant="ghost"
            className="shrink-0 text-gray-600"
            onClick={() => window.open(googleCalendarLink, '_blank')}
          >
            <Image src="google-calendar.svg" alt="Google Calendar Icon" width={20} height={20} />
            Add to Google Calendar
          </Button>
        )}
      </div>
      <p className="text-gray-600 text-sm leading-[21px] font-[Quasimoda]">Description</p>
      {todo.description && (
        <p className="text-sm font-[Quasimoda] leading-[21px] text-gray-800 mb-2">
          {todo.description}
        </p>
      )}
      {todo.type === 'systemCreated' && (
        <div className="mt-4">
          <ApprovalActions todo={todo} onReject={onClose} />
        </div>
      )}
      {chatMessages && (
        <div className="mt-10">
          <p className="font-[Quasimoda] text-xl text-gray-400 font-bold mb-2.5 flex justify-between items-center">
            Last message from Alfie
            <HugeiconsIcon
              icon={LinkSquare01Icon as unknown as IconSvgObject}
              size={24}
              color="var(--colors-blue-1000)"
              onClick={() => {
                if (chatMessages?.id) {
                  window.open(`/chats/${chatMessages.id}`, '_blank');
                }
              }}
            />
          </p>
          <div className="max-h-[300px] overflow-x-auto pb-[100px]">
            <Message
              message={chatMessages?.lastMessages?.[0]}
              messages={chatMessages?.lastMessages ?? []}
            />
          </div>
        </div>
      )}
      <div className="w-full absolute bottom-0 left-0 right-0 p-4 bg-white shadow--[0px_-3px_8.1px_0px_rgba(20,27,52,0.08)] rounded-xl">
        {!chatMessages && (
          <p className="font-[Quasimoda] text-xl leading-[30px] font-bold mb-3">
            What can I help you with?
          </p>
        )}
        <Composer />
      </div>
    </div>
  );
};
