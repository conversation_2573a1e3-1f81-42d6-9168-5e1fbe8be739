'use client';

import { Dialog, DialogContent } from '@/components/ui/dialog';
import { useEffect, useMemo, useState } from 'react';
import { TodoDetailsContent } from './TodoDetailsContent';
import { ITodoDto, IUpdateTodoDto } from '@/api/todos/types';
import useSWR, { mutate } from 'swr';
import { API_ENDPOINTS, getApiUrl } from '@/constants/api';
import { fetchTodoDetails, updateTodo } from '@/api/todos/todos';
import { useAuth } from '@clerk/nextjs';
import { Button } from '@/components/ui/button';
import { Tick02Icon, PencilEdit01Icon, Delete02Icon } from '@hugeicons-pro/core-stroke-standard';
import * as SolidStandard from '@hugeicons-pro/core-solid-standard';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import { IconSvgObject } from '@hugeicons/react';
import { EditTodoDetails } from './EditTodoDetails';
import { Spinner } from '@/components/Spinner';
import useSWRMutation from 'swr/mutation';
import { toNaiveISOString } from '@/lib/toNaiveISOString';
import { toast } from '@/components/ui/sonner';
import { useTodoContext } from '../../useTodoContext';
import { useDeleteTodo } from '@/hooks/useTodoService';
import { useGetEntityLink } from '@/hooks/useEntityLinkService';
import { parseAsInteger, useQueryState } from 'nuqs';

interface TodoDetailsProps {
  isOpen: boolean;
  todoId: ITodoDto['id'] | null;
  onClose?: () => void;
}

export const TodoDetails: React.FC<TodoDetailsProps> = ({ isOpen, todoId, onClose }) => {
  const { getToken } = useAuth();
  const [isEditMode, setIsEditMode] = useState(false);
  const { confirmAction, isEditFormDirty } = useTodoContext();
  const [_, setSelectedChatId] = useQueryState<number>('chatId', parseAsInteger);

  const fetchTodoDetailsWithGetToken = async (url: string) => {
    const freshToken = await getToken();

    return fetchTodoDetails(url, freshToken!);
  };

  const updateTodoWithGetToken = async (url: string, options: { arg: IUpdateTodoDto }) => {
    const freshToken = await getToken();

    return updateTodo(url, { arg: { token: freshToken!, updateTodo: options.arg } });
  };

  const { data: todo, isLoading } = useSWR(
    todoId ? getApiUrl(`${API_ENDPOINTS.TODOS}/${todoId}`) : null,
    fetchTodoDetailsWithGetToken
  );
  const { trigger: markTodoAsDone } = useSWRMutation(
    todoId ? getApiUrl(`${API_ENDPOINTS.TODOS}/${todoId}`) : null,
    updateTodoWithGetToken
  );
  const { data: entityLinks } = useGetEntityLink({
    entityId: todo?.id ?? -1,
    entityType: 'todos',
  });
  const { trigger: deleteTodo } = useDeleteTodo();
  const handleEditClick = () => {
    setIsEditMode(true);
  };

  const handleMarkAsDone = async () => {
    try {
      await confirmAction('markAsDone', null);
      // do nothing, the user cancelled the action
    } catch (_err) {
      if (!todo) return;

      await markTodoAsDone({
        ...todo,
        doneDate: toNaiveISOString(new Date()),
      });
      mutate(getApiUrl(API_ENDPOINTS.TODOS_ACCEPTED));
      onClose?.();
    }
  };

  const handleDeleteClick = async () => {
    try {
      await confirmAction('removeTodo', null);
      // do nothing, the user cancelled the action
    } catch (_err) {
      if (!todo) return;

      await deleteTodo({
        todoId: todo.id,
      });
      mutate(getApiUrl(API_ENDPOINTS.TODOS_ACCEPTED));
      onClose?.();

      toast.info({ title: 'To-do deleted' });
    }
  };

  const handleSaveClick = () => {
    setIsEditMode(false);
  };

  const handleCloseModal = () => {
    if (isEditMode) {
      if (!isEditFormDirty) {
        return setIsEditMode(false);
      }

      confirmAction('editTodo', null)
        .then(() => {
          // User cancelled the action, do nothing
        })
        .catch(() => {
          setIsEditMode(false);
        });
    } else {
      onClose?.();
    }
  };

  useEffect(() => {
    if (entityLinks?.[0]?.id) {
      setSelectedChatId(entityLinks[0].id);
    }
  }, [setSelectedChatId, entityLinks]);

  const chatMessages = useMemo(
    () => entityLinks?.filter((entity) => entity.entityType === 'chats')?.[0],
    [entityLinks]
  );

  return (
    <Dialog open={isOpen}>
      <DialogContent className="lg:!max-w-3xl" showCloseButton={false}>
        <div className="absolute top-4 right-4 flex gap-4 items-center">
          {!isLoading && !isEditMode && todo?.type !== 'systemCreated' && (
            <>
              <Button
                size="xs"
                variant="ghost"
                onClick={handleMarkAsDone}
                disabled={!!todo?.doneDate}
              >
                <HugeiconsIcon icon={Tick02Icon as unknown as IconSvgObject} size={12} /> Mark as
                Done
              </Button>
              <Button size="icon" variant="link" onClick={handleEditClick}>
                <HugeiconsIcon icon={PencilEdit01Icon as unknown as IconSvgObject} size={24} />
              </Button>
              <Button size="icon" variant="link" onClick={handleDeleteClick}>
                <HugeiconsIcon
                  icon={Delete02Icon as unknown as IconSvgObject}
                  size={24}
                  color="var(--color-red-500)"
                />
              </Button>
            </>
          )}
          <Button size="icon" variant="link" onClick={() => handleCloseModal()}>
            <HugeiconsIcon
              icon={SolidStandard.MultiplicationSignIcon as unknown as IconSvgObject}
              size={24}
            />
          </Button>
        </div>
        {isLoading || !todo ? (
          <div className="w-full h-full flex items-center justify-center">
            <Spinner color="var(--colors-yellow-500)" size={50} />
          </div>
        ) : (
          <>
            {isEditMode ? (
              <EditTodoDetails todo={todo} onClick={handleSaveClick} />
            ) : (
              <TodoDetailsContent todo={todo} chatMessages={chatMessages} onClose={onClose} />
            )}
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};
