'use client';

import React, { useLayoutEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { RedirectToSignIn, SignedIn, SignedOut, useUser } from '@clerk/nextjs';
import { useBreakpoint } from '@/utils/breakpointUtils';
import { Onboarding } from '@/components/Onboarding/Onboarding';
import styles from '@/containers/OnboardingPage/OnboardingPage.module.scss';
import { Spinner } from '@/components/Spinner';

export default function OnboardingPage() {
  const { user, isLoaded } = useUser();
  const router = useRouter();
  const { isMobile } = useBreakpoint();
  const [visitedOnboarding, setVisitedOnboarding] = useState(!!user?.unsafeMetadata?.visitedOnboarding);

  useLayoutEffect(() => {
    if (!isLoaded || !user) return;
    if (visitedOnboarding) {
      router.replace('/');
    }
    else {
      void user.update({
        unsafeMetadata: {
          ...user.unsafeMetadata,
          visitedOnboarding: true
        }
      });
    }
  }, [isLoaded, user, visitedOnboarding]);

  const completeOnboarding = async (addressRequest: Promise<unknown>) => {
    try {
      await Promise.all([addressRequest]);
    } catch (error) {
      console.error('Error during onboarding completion:', error);
    }
  };

  return (
    <>
      <SignedIn>
        <div className={styles.container}>
          {!isMobile && (
            <div className={styles.header}>
              <img src="/heyAlfie.svg" alt="heyAlfie" className={styles.image} />
            </div>
          )}
          <div className={isMobile ? styles.mobileOnboarding : styles.onboarding}>
            {visitedOnboarding ? (
              <div className="w-screen h-screen absolute top-0 left-0 flex items-center justify-center">
                <Spinner color="var(--colors-yellow-500)" size={100} />
              </div>
            ) : (
              <Onboarding onComplete={completeOnboarding} />
            )}
          </div>
        </div>
      </SignedIn>
      <SignedOut>
        <RedirectToSignIn />
      </SignedOut>
    </>
  );
}
