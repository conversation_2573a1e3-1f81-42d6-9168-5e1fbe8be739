export const API_BASE_URL =
  process.env.NEXT_PUBLIC_VERCEL_ENV === 'production'
    ? 'https://backend.api.heyalfie.com'
    : (process.env.NEXT_PUBLIC_CUSTOM_API_URL ?? 'https://backend.api.staging.heyalfie.com');

export const getApiUrl = (path: string): string => {
  // Remove leading slash if present
  const sanitizedPath = path.startsWith('/') ? path.substring(1) : path;
  return `${API_BASE_URL}/${sanitizedPath}`;
};

// Common API endpoints
export const API_ENDPOINTS = {
  CHATS: 'chats',
  MESSAGES: 'messages',
  DOCUMENTS: 'documents',
  PROPERTIES: 'properties',
  APPLIANCES: 'appliances',
  JOBS: 'jobs',
  EXTERNAL_DATASOURCES: 'external-datasources',
  DEMO_REQUEST: 'demo-request',
  USER: 'user',
  USER_GUEST: 'user/guest',
  TODOS: 'todos',
  TODOS_SUGGESTED: 'todos/suggested',
  TODOS_ACCEPTED: 'todos/accepted',
  TODO_ACCEPT: 'todos/{todoId}/accept',
  TODO_REJECT: 'todos/{todoId}/reject',
  NOTIFICATIONS: 'notifications',
  ENTITY_LINKS: 'entity-links',
};
