import { api } from '@/lib/api';
import { ICreateEntityLinkDto, IGetEntityLinkDto, IGetEntityLinkResponseDto } from './types';

export const createEntityLink = async (
  url: string,
  { arg }: { arg: { token: string; linkingEntity: ICreateEntityLinkDto } }
): Promise<void> => {
  const { entityTypeA, entityTypeB, entityIdA, entityIdB } = arg.linkingEntity;
  await api.post<void>(
    `${url}/${entityTypeA}/${entityIdA}/${entityTypeB}/${entityIdB}`,
    {},
    {
      headers: {
        Authorization: `Bearer ${arg.token}`,
        Accept: 'application/json',
      },
    }
  );
};

export const getEntityLink = async (
  url: string,
  {
    arg,
  }: {
    arg: {
      token: string;
      linkingEntity: IGetEntityLinkDto;
    };
  }
): Promise<IGetEntityLinkResponseDto> => {
  const { entityType, entityId } = arg.linkingEntity;

  const response = await api.get<IGetEntityLinkResponseDto>(`${url}/${entityType}/${entityId}`, {
    headers: {
      Authorization: `Bearer ${arg.token}`,
      Accept: 'application/json',
    },
  });

  return response.data;
};
