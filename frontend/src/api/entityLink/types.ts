import { Message } from '@/types/messages';

type EntityType =
  | 'users'
  | 'jobs'
  | 'chats'
  | 'appliances'
  | 'documents'
  | 'insurances'
  | 'legals'
  | 'projects'
  | 'todos';

export interface ChatLinkedEntityInfo {
  entityType: 'chats';
  id: number;
  entity: {
    id: number;
    title: string;
    status: string;
  };
  lastMessages: Message[] | null;
}

export interface TodoLinkedEntityInfo {
  entityType: 'todos';
  id: number;
  entity: {
    id: number;
    name: string;
    description: string;
    dueDate: string | null;
    doneDate: string | null;
    type: string;
    deletedDate: string | null;
  };
}

export interface ICreateEntityLinkDto {
  entityTypeA: EntityType;
  entityTypeB: EntityType;
  entityIdA: number;
  entityIdB: number;
}

export interface IGetEntityLinkDto {
  entityType: EntityType;
  entityId: number;
}

export type IGetEntityLinkResponseDto = ChatLinkedEntityInfo[] | TodoLinkedEntityInfo[];
