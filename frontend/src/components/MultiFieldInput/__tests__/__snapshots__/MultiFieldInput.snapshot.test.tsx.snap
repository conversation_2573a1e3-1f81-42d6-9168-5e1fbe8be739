// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`MultiFieldInput Snapshots > matches snapshot with access instruction and value 1`] = `
<div>
  <div
    class="_container_b01ac6"
  >
    <div
      class="_title_b01ac6"
    >
      Access instructions (optional)
    </div>
    <div
      class="_inputContainer_b01ac6"
    >
      <textarea
        class="_input_b01ac6 _accessInstructionTextarea_b01ac6"
        placeholder="For example: Instructions about access, parking, or pets."
        rows="3"
        style="height: 96px; resize: none;"
      >
        Please use the side entrance and ring the bell twice.
      </textarea>
      <div
        class="_buttonContainer_b01ac6 _actionsContainer_b01ac6 _buttonLeft_b01ac6"
      >
        <button
          color="green-primary"
          data-testid="mock-button"
          state="default"
          type="primary"
        >
          Save instructions
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiFieldInput Snapshots > matches snapshot with access instruction type 1`] = `
<div>
  <div
    class="_container_b01ac6"
  >
    <div
      class="_title_b01ac6"
    >
      Access instructions (optional)
    </div>
    <div
      class="_inputContainer_b01ac6"
    >
      <textarea
        class="_input_b01ac6 _accessInstructionTextarea_b01ac6"
        placeholder="For example: Instructions about access, parking, or pets."
        rows="3"
        style="height: 96px; resize: none;"
      />
      <div
        class="_buttonContainer_b01ac6 _actionsContainer_b01ac6 _buttonLeft_b01ac6"
      >
        <button
          color="green-primary"
          data-testid="mock-button"
          state="default"
          type="primary"
        >
          Save instructions
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiFieldInput Snapshots > matches snapshot with address input in manual mode 1`] = `
<div>
  <div
    class="_container_b01ac6"
  >
    <div
      class="_inputContainer_b01ac6"
    >
      <div
        class="_addressFields_b01ac6"
      >
        <div
          class="_fieldGroup_b01ac6"
        >
          <label>
            Address Line 1
          </label>
          <input
            class="_input_b01ac6"
            placeholder="Line 1"
            type="text"
            value=""
          />
        </div>
        <div
          class="_fieldGroup_b01ac6"
        >
          <label>
            Address Line 2 (Optional)
          </label>
          <input
            class="_input_b01ac6"
            placeholder="Line 2"
            type="text"
            value=""
          />
        </div>
        <div
          class="_fieldGroup_b01ac6"
        >
          <label>
            City
          </label>
          <input
            class="_input_b01ac6"
            placeholder="City"
            type="text"
            value=""
          />
        </div>
        <div
          class="_fieldGroup_b01ac6"
        >
          <label>
            Postcode
          </label>
          <input
            class="_input_b01ac6"
            placeholder="Postcode"
            type="text"
            value=""
          />
        </div>
        <div
          class="_actionsContainer_b01ac6 _manualActions_b01ac6"
        >
          <div
            class="_manualEntry_b01ac6 _addressLookup_b01ac6"
          >
            Use address lookup
          </div>
          <button
            color="green-primary"
            data-testid="mock-button"
            state="default"
            type="primary"
          >
            Save Address
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiFieldInput Snapshots > matches snapshot with address input in select mode 1`] = `
<div>
  <div
    class="_container_b01ac6"
  >
    <div
      class="_title_b01ac6"
    >
      Select your address
    </div>
    <div
      class="_inputContainer_b01ac6"
    >
      <div
        class="_selectContainer_b01ac6"
      >
        <div
          aria-label="Select your address"
          class="_input_b01ac6 _selectInput_b01ac6 _empty_b01ac6"
          contenteditable="true"
          data-placeholder="Select your address"
        />
      </div>
      <div
        class="_actionsContainer_b01ac6"
      >
        <div
          class="_manualEntry_b01ac6"
        >
          Enter manually
        </div>
        <div
          class="_buttonContainer_b01ac6"
        >
          <button
            color="green-primary"
            data-testid="mock-button"
            state="default"
            type="primary"
          >
            Save Address
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiFieldInput Snapshots > matches snapshot with address input with values 1`] = `
<div>
  <div
    class="_container_b01ac6"
  >
    <div
      class="_inputContainer_b01ac6"
    >
      <div
        class="_addressFields_b01ac6"
      >
        <div
          class="_fieldGroup_b01ac6"
        >
          <label>
            Address Line 1
          </label>
          <input
            class="_input_b01ac6"
            placeholder="Line 1"
            type="text"
            value="123 Main Street"
          />
        </div>
        <div
          class="_fieldGroup_b01ac6"
        >
          <label>
            Address Line 2 (Optional)
          </label>
          <input
            class="_input_b01ac6"
            placeholder="Line 2"
            type="text"
            value="Apartment 4B"
          />
        </div>
        <div
          class="_fieldGroup_b01ac6"
        >
          <label>
            City
          </label>
          <input
            class="_input_b01ac6"
            placeholder="City"
            type="text"
            value="London"
          />
        </div>
        <div
          class="_fieldGroup_b01ac6"
        >
          <label>
            Postcode
          </label>
          <input
            class="_input_b01ac6"
            placeholder="Postcode"
            type="text"
            value="SW1A 1AA"
          />
        </div>
        <div
          class="_actionsContainer_b01ac6 _manualActions_b01ac6"
        >
          <div
            class="_manualEntry_b01ac6 _addressLookup_b01ac6"
          >
            Use address lookup
          </div>
          <button
            color="green-primary"
            data-testid="mock-button"
            state="default"
            type="primary"
          >
            Save Address
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiFieldInput Snapshots > matches snapshot with address options 1`] = `
<div>
  <div
    class="_container_b01ac6"
  >
    <div
      class="_title_b01ac6"
    >
      Select your address
    </div>
    <div
      class="_inputContainer_b01ac6"
    >
      <div
        class="_selectContainer_b01ac6"
      >
        <div
          aria-label="Select your address"
          class="_input_b01ac6 _selectInput_b01ac6 _empty_b01ac6"
          contenteditable="true"
          data-placeholder="Select your address"
        />
      </div>
      <div
        class="_actionsContainer_b01ac6"
      >
        <div
          class="_manualEntry_b01ac6"
        >
          Enter manually
        </div>
        <div
          class="_buttonContainer_b01ac6"
        >
          <button
            color="green-primary"
            data-testid="mock-button"
            state="default"
            type="primary"
          >
            Save Address
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiFieldInput Snapshots > matches snapshot with children 1`] = `
<div>
  <div
    class="_container_b01ac6"
  >
    <div
      class="_title_b01ac6"
    >
      Enter your phone number
    </div>
    <div
      class="_inputContainer_b01ac6"
    >
      <input
        class="_input_b01ac6"
        placeholder="+44XXXXXXXXXX"
        type="tel"
        value=""
      />
      <div
        class="_buttonContainer_b01ac6 _actionsContainer_b01ac6 _buttonLeft_b01ac6 _buttonWithMargin_b01ac6"
      >
        <button
          color="green-primary"
          data-testid="mock-button"
          state="disabled"
          type="primary"
        >
          Verify phone number
        </button>
      </div>
    </div>
    <div>
      Custom child content
    </div>
  </div>
</div>
`;

exports[`MultiFieldInput Snapshots > matches snapshot with compact mode 1`] = `
<div>
  <div
    class="_container_b01ac6 _compact_b01ac6"
  >
    <div
      class="_title_b01ac6"
    >
      Enter your phone number
    </div>
    <div
      class="_inputContainer_b01ac6"
    >
      <input
        class="_input_b01ac6"
        placeholder="+44XXXXXXXXXX"
        type="tel"
        value=""
      />
      <div
        class="_buttonContainer_b01ac6 _actionsContainer_b01ac6 _buttonLeft_b01ac6 _buttonWithMargin_b01ac6"
      >
        <button
          color="green-primary"
          data-testid="mock-button"
          state="disabled"
          type="primary"
        >
          Verify phone number
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiFieldInput Snapshots > matches snapshot with container className 1`] = `
<div>
  <div
    class="_container_b01ac6 custom-container-class"
  >
    <div
      class="_title_b01ac6"
    >
      Enter your phone number
    </div>
    <div
      class="_inputContainer_b01ac6"
    >
      <input
        class="_input_b01ac6"
        placeholder="+44XXXXXXXXXX"
        type="tel"
        value=""
      />
      <div
        class="_buttonContainer_b01ac6 _actionsContainer_b01ac6 _buttonLeft_b01ac6 _buttonWithMargin_b01ac6"
      >
        <button
          color="green-primary"
          data-testid="mock-button"
          state="disabled"
          type="primary"
        >
          Verify phone number
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiFieldInput Snapshots > matches snapshot with custom className 1`] = `
<div>
  <div
    class="_container_b01ac6 custom-class"
  >
    <div
      class="_title_b01ac6"
    >
      Enter your phone number
    </div>
    <div
      class="_inputContainer_b01ac6"
    >
      <input
        class="_input_b01ac6"
        placeholder="+44XXXXXXXXXX"
        type="tel"
        value=""
      />
      <div
        class="_buttonContainer_b01ac6 _actionsContainer_b01ac6 _buttonLeft_b01ac6 _buttonWithMargin_b01ac6"
      >
        <button
          color="green-primary"
          data-testid="mock-button"
          state="disabled"
          type="primary"
        >
          Verify phone number
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiFieldInput Snapshots > matches snapshot with custom save button text 1`] = `
<div>
  <div
    class="_container_b01ac6"
  >
    <div
      class="_title_b01ac6"
    >
      Enter your phone number
    </div>
    <div
      class="_inputContainer_b01ac6"
    >
      <input
        class="_input_b01ac6"
        placeholder="+44XXXXXXXXXX"
        type="tel"
        value=""
      />
      <div
        class="_buttonContainer_b01ac6 _actionsContainer_b01ac6 _buttonLeft_b01ac6 _buttonWithMargin_b01ac6"
      >
        <button
          color="green-primary"
          data-testid="mock-button"
          state="disabled"
          type="primary"
        >
          Verify phone number
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiFieldInput Snapshots > matches snapshot with custom title and placeholder 1`] = `
<div>
  <div
    class="_container_b01ac6"
  >
    <div
      class="_title_b01ac6"
    >
      Custom Phone Title
    </div>
    <div
      class="_inputContainer_b01ac6"
    >
      <input
        class="_input_b01ac6"
        placeholder="Enter phone number"
        type="tel"
        value=""
      />
      <div
        class="_buttonContainer_b01ac6 _actionsContainer_b01ac6 _buttonLeft_b01ac6 _buttonWithMargin_b01ac6"
      >
        <button
          color="green-primary"
          data-testid="mock-button"
          state="disabled"
          type="primary"
        >
          Verify phone number
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiFieldInput Snapshots > matches snapshot with default phone input 1`] = `
<div>
  <div
    class="_container_b01ac6"
  >
    <div
      class="_title_b01ac6"
    >
      Enter your phone number
    </div>
    <div
      class="_inputContainer_b01ac6"
    >
      <input
        class="_input_b01ac6"
        placeholder="+44XXXXXXXXXX"
        type="tel"
        value=""
      />
      <div
        class="_buttonContainer_b01ac6 _actionsContainer_b01ac6 _buttonLeft_b01ac6 _buttonWithMargin_b01ac6"
      >
        <button
          color="green-primary"
          data-testid="mock-button"
          state="disabled"
          type="primary"
        >
          Verify phone number
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiFieldInput Snapshots > matches snapshot with disabled state 1`] = `
<div>
  <div
    class="_container_b01ac6"
  >
    <div
      class="_title_b01ac6"
    >
      Enter your phone number
    </div>
    <div
      class="_inputContainer_b01ac6"
    >
      <input
        class="_input_b01ac6 _disabled_b01ac6"
        disabled=""
        placeholder="+44XXXXXXXXXX"
        type="tel"
        value=""
      />
      <div
        class="_buttonContainer_b01ac6 _actionsContainer_b01ac6 _buttonLeft_b01ac6 _buttonWithMargin_b01ac6"
      >
        <button
          color="green-primary"
          data-testid="mock-button"
          state="disabled"
          type="primary"
        >
          Verify phone number
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiFieldInput Snapshots > matches snapshot with email input and value 1`] = `
<div>
  <div
    class="_container_b01ac6"
  >
    <div
      class="_title_b01ac6"
    >
      Enter your email address
    </div>
    <div
      class="_inputContainer_b01ac6"
    >
      <div
        class="_emailText_b01ac6"
      >
        <EMAIL>
      </div>
      <div
        class="_buttonContainer_b01ac6 _actionsContainer_b01ac6 _buttonLeft_b01ac6 _buttonWithMargin_b01ac6"
      >
        <button
          color="green-primary"
          data-testid="mock-button"
          state="default"
          type="primary"
        >
          Verify email
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiFieldInput Snapshots > matches snapshot with email input type 1`] = `
<div>
  <div
    class="_container_b01ac6"
  >
    <div
      class="_title_b01ac6"
    >
      Enter your email address
    </div>
    <div
      class="_inputContainer_b01ac6"
    >
      <div
        class="_emailText_b01ac6"
      >
        —
      </div>
      <div
        class="_buttonContainer_b01ac6 _actionsContainer_b01ac6 _buttonLeft_b01ac6 _buttonWithMargin_b01ac6"
      >
        <button
          color="green-primary"
          data-testid="mock-button"
          state="disabled"
          type="primary"
        >
          Verify email
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiFieldInput Snapshots > matches snapshot with error state 1`] = `
<div>
  <div
    class="_container_b01ac6"
  >
    <div
      class="_title_b01ac6"
    >
      Enter your phone number
    </div>
    <div
      class="_inputContainer_b01ac6"
    >
      <input
        class="_input_b01ac6"
        placeholder="+44XXXXXXXXXX"
        type="tel"
        value=""
      />
      <div
        class="_error_b01ac6"
      >
        This field is required
      </div>
      <div
        class="_buttonContainer_b01ac6 _actionsContainer_b01ac6 _buttonLeft_b01ac6"
      >
        <button
          color="green-primary"
          data-testid="mock-button"
          state="disabled"
          type="primary"
        >
          Verify phone number
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiFieldInput Snapshots > matches snapshot with hidden border 1`] = `
<div>
  <div
    class="_container_b01ac6 _noBorder_b01ac6"
  >
    <div
      class="_title_b01ac6"
    >
      Enter your phone number
    </div>
    <div
      class="_inputContainer_b01ac6"
    >
      <input
        class="_input_b01ac6"
        placeholder="+44XXXXXXXXXX"
        type="tel"
        value=""
      />
      <div
        class="_buttonContainer_b01ac6 _actionsContainer_b01ac6 _buttonLeft_b01ac6 _buttonWithMargin_b01ac6"
      >
        <button
          color="green-primary"
          data-testid="mock-button"
          state="disabled"
          type="primary"
        >
          Verify phone number
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiFieldInput Snapshots > matches snapshot with hidden save button 1`] = `
<div>
  <div
    class="_container_b01ac6"
  >
    <div
      class="_title_b01ac6"
    >
      Enter your phone number
    </div>
    <div
      class="_inputContainer_b01ac6"
    >
      <input
        class="_input_b01ac6"
        placeholder="+44XXXXXXXXXX"
        type="tel"
        value=""
      />
      <div
        class="_buttonContainer_b01ac6 _actionsContainer_b01ac6 _buttonLeft_b01ac6 _buttonWithMargin_b01ac6"
      >
        <button
          color="green-primary"
          data-testid="mock-button"
          state="disabled"
          type="primary"
        >
          Verify phone number
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiFieldInput Snapshots > matches snapshot with hidden title 1`] = `
<div>
  <div
    class="_container_b01ac6"
  >
    <div
      class="_inputContainer_b01ac6"
    >
      <input
        class="_input_b01ac6"
        placeholder="+44XXXXXXXXXX"
        type="tel"
        value=""
      />
      <div
        class="_buttonContainer_b01ac6 _actionsContainer_b01ac6 _buttonLeft_b01ac6 _buttonWithMargin_b01ac6"
      >
        <button
          color="green-primary"
          data-testid="mock-button"
          state="disabled"
          type="primary"
        >
          Verify phone number
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiFieldInput Snapshots > matches snapshot with loading state 1`] = `
<div>
  <div
    class="_container_b01ac6"
  >
    <div
      class="_title_b01ac6"
    >
      Enter your phone number
    </div>
    <div
      class="_inputContainer_b01ac6"
    >
      <input
        class="_input_b01ac6"
        disabled=""
        placeholder="+44XXXXXXXXXX"
        type="tel"
        value=""
      />
      <div
        class="_buttonContainer_b01ac6 _actionsContainer_b01ac6 _buttonLeft_b01ac6 _buttonWithMargin_b01ac6"
      >
        <button
          color="green-primary"
          data-testid="mock-button"
          state="disabled"
          type="primary"
        >
          Verify phone number
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiFieldInput Snapshots > matches snapshot with manual entry text 1`] = `
<div>
  <div
    class="_container_b01ac6"
  >
    <div
      class="_title_b01ac6"
    >
      Select your address
    </div>
    <div
      class="_inputContainer_b01ac6"
    >
      <div
        class="_selectContainer_b01ac6"
      >
        <div
          aria-label="Select your address"
          class="_input_b01ac6 _selectInput_b01ac6 _empty_b01ac6"
          contenteditable="true"
          data-placeholder="Select your address"
        />
      </div>
      <div
        class="_actionsContainer_b01ac6"
      >
        <div
          class="_manualEntry_b01ac6"
        >
          Enter address manually
        </div>
        <div
          class="_buttonContainer_b01ac6"
        >
          <button
            color="green-primary"
            data-testid="mock-button"
            state="default"
            type="primary"
          >
            Save Address
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiFieldInput Snapshots > matches snapshot with phone input and value 1`] = `
<div>
  <div
    class="_container_b01ac6"
  >
    <div
      class="_title_b01ac6"
    >
      Enter your phone number
    </div>
    <div
      class="_inputContainer_b01ac6"
    >
      <input
        class="_input_b01ac6"
        placeholder="+44XXXXXXXXXX"
        type="tel"
        value="+441234567890"
      />
      <div
        class="_buttonContainer_b01ac6 _actionsContainer_b01ac6 _buttonLeft_b01ac6 _buttonWithMargin_b01ac6"
      >
        <button
          color="green-primary"
          data-testid="mock-button"
          state="default"
          type="primary"
        >
          Verify phone number
        </button>
      </div>
    </div>
  </div>
</div>
`;
