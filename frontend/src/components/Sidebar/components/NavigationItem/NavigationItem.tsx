'use client';

import React, { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react';
import Link from 'next/link';
import classNames from 'classnames';
import { IconSvgObject } from '@hugeicons/react';
import { SquareLock02Icon } from '@hugeicons-pro/core-stroke-standard';
import { LinkSquare01Icon } from '@hugeicons-pro/core-stroke-rounded';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import styles from './NavigationItem.module.scss';

interface NavigationItemProps {
  icon: React.ReactNode;
  label: string;
  isAuthenticated: boolean;
  isComingSoon?: boolean;
  onClick?: (e: MouseEvent<HTMLAnchorElement, MouseEvent>) => void;
  isButton?: boolean;
  className?: string;
  url?: string;
  guestAccessible?: boolean;
  isExternal?: boolean;
}

export const NavigationItem: React.FC<NavigationItemProps> = ({
  icon,
  label,
  isAuthenticated,
  isComingSoon = false,
  onClick,
  isButton = false,
  classN<PERSON>,
  url,
  guestAccessible = false,
  isExternal = false,
}) => {
  const content = (
    <>
      {icon}
      <span className={styles.navText}>{label}</span>
      {!isAuthenticated && !guestAccessible && (
        <HugeiconsIcon
          icon={SquareLock02Icon as unknown as IconSvgObject}
          size={20}
          className={styles.lockIcon}
        />
      )}
      {isAuthenticated && isComingSoon && <span className={styles.comingSoon}>Coming soon</span>}
      {isExternal && (
        <HugeiconsIcon
          icon={LinkSquare01Icon as unknown as IconSvgObject}
          size={16}
          className={styles.externalLinkIcon}
        />
      )}
    </>
  );

  const isDisabled = (!isAuthenticated && !guestAccessible) || (isAuthenticated && isComingSoon);
  const itemClassName = classNames(
    styles.navItem,
    {
      [styles.disabled]: isDisabled,
      [styles.isComingSoonNav]: isAuthenticated && isComingSoon,
    },
    className
  );

  const handleClick = !isDisabled ? onClick : undefined;

  if (isButton) {
    return (
      <button
        className={itemClassName}
        onClick={handleClick as unknown as MouseEventHandler<HTMLButtonElement>}
        disabled={isDisabled}
        type="button"
      >
        {content}
      </button>
    );
  }

  if (url && !isComingSoon) {
    if (isExternal) {
      return (
        <a
          href={url}
          className={itemClassName}
          target="_blank"
          rel="noopener noreferrer"
          onClick={(e) => {
            if (onClick) {
              onClick(e as unknown as MouseEvent<HTMLAnchorElement, MouseEvent>);
            }
          }}
        >
          {content}
        </a>
      );
    }

    return (
      <Link
        href={url}
        className={itemClassName}
        onClick={(e) => {
          if (onClick) {
            e.preventDefault();
            onClick(e as unknown as MouseEvent<HTMLAnchorElement, MouseEvent>);
          }
        }}
      >
        {content}
      </Link>
    );
  }

  return <div className={itemClassName}>{content}</div>;
};
