.navItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.6rem 0 0.25rem 0.625rem;
  border-radius: 0.25rem;
  position: relative;
  width: 100%;
  border: none;
  background: none;

  svg {
    color: #111928;
  }

  .externalLinkIcon svg {
    color: var(--colors-gray-400);
  }

  &:not(.disabled):hover {
    background-color: #f3f4f6;
    cursor: pointer;
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;

    svg,
    .navText {
      color: #6b7280;
    }
  }

  &:not(.isComingSoonNav) {
    padding: 0.6rem 0 0.5rem 0.625rem;
  }

  &.isComingSoonNav {
    .navText {
      cursor: not-allowed;
      flex: 0 1 auto;
      display: inline-flex;
      align-items: center;
      gap: 8px;
    }
  }
}

.navText {
  font-family: 'Quasimoda';
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  color: #111928;
  flex: 1;
}

.lockIcon {
  position: static;
  color: #6b7280;
  margin-left: auto;
}

.externalLinkIcon {
  position: static;
  color: var(--colors-gray-500) !important;
  margin-left: auto;
  margin-right: 8px;
  flex-shrink: 0;

  svg {
    color: var(--colors-gray-500) !important;
  }
}

.settingsLockIcon {
  position: static;
  margin-left: auto;
}

.comingSoon {
  font-family: 'Quasimoda';
  font-size: 12px;
  background-color: #ffebce;
  color: #d28e28;
  border: 1px solid #d28e28;
  border-radius: 4px;
  font-weight: 400;
  line-height: 150%;
  letter-spacing: 0%;
  padding: 4px 8px;
  white-space: nowrap;
}

.disabled {
  cursor: not-allowed;
}
