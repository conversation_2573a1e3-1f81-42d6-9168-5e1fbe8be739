'use client';

import React, { useCallback, useState } from 'react';
import { useOnboarding } from '@/hooks/useOnboarding';
import { OnboardingStepper } from './components/OnboardingStepper/OnboardingStepper';
import { OnboardingHeader } from './components/OnboardingHeader/OnboardingHeader';
import { OnboardingSteps } from './components/OnboardingSteps/OnboardingSteps';
import styles from './Onboarding.module.scss';
import { Button } from '../Button/Button';
import { ButtonColor, ButtonSize, ButtonState, ButtonType } from '../Button/Button.types';
import { AddressFields } from '../MultiFieldInput/MultiFieldInput.types';
import { OnboardingDemo, useDemoForm } from './components/OnboardingDemo/OnboardingDemo';
import { useSendOnboardingData } from '@/hooks/useSendOnboardingData';
import { UserPropertyRelationType } from '@/api/properties';

const EMPTY_ADDRESS: AddressFields = {
  line1: '',
  line2: '',
  city: '',
  postcode: '',
};

interface OnboardingProps {
  onComplete?: (addressSendRequest: Promise<unknown>) => void;
}

export const Onboarding: React.FC<OnboardingProps> = ({ onComplete }) => {
  const {
    // Navigation
    steps,
    currentStep,
    goToStep,
    nextStep,

    // Onboarding data
    selectedRole,
    selectedAddress,
    selectedPropertyType,
    selectedOwnershipType,

    // Actions
    setSelectedRole,
    setSelectedAddress,
    setSelectedPropertyType,
    setSelectedOwnershipType,
    clearData,
  } = useOnboarding();

  const [manualAddressData, setManualAddressData] = useState<AddressFields>(EMPTY_ADDRESS);
  const [managerSingleProperty, setManagerSingleProperty] = useState<boolean>(false);

  const demoForm = useDemoForm();
  const [demoLoading, setDemoLoading] = useState(false);
  const [demoSuccess, setDemoSuccess] = useState(false);
  const [demoError, setDemoError] = useState<string | null>(null);
  const { sendOnboardingData, sendDemoRequest } = useSendOnboardingData();

  const handleNext = async () => {
    if (currentStep === steps.length - 2 && onComplete) {
      const areAllOptionsSelected =
        !selectedRole || !selectedPropertyType || !selectedOwnershipType;
      if (areAllOptionsSelected) {
        return;
      }
      const { addressSendRequest } = await sendOnboardingData({
        selectedAddress,
        selectedRole,
        selectedPropertyType,
        selectedOwnershipType,
      });
      onComplete(addressSendRequest);
    }
    nextStep();
  };

  const handleBack = useCallback(() => {
    if (currentStep === 2) {
      clearData('propertyTypes');
    } else if (currentStep === 1) {
      clearData('address');
    }
    goToStep(currentStep - 1);
  }, [currentStep, clearData, goToStep]);

  const handleRoleSelected = useCallback(
    (role: UserPropertyRelationType) => {
      setSelectedRole(role);
    },
    [setSelectedRole]
  );

  const handleAddressSelected = useCallback(
    (address: string | AddressFields) => {
      setSelectedAddress(address);
      if (typeof address !== 'string') {
        setManualAddressData(address);
      }
    },
    [setSelectedAddress]
  );

  const handleDemoSubmit = async () => {
    setDemoLoading(true);
    setDemoError(null);
    try {
      const { demoSendRequest } = await sendDemoRequest({
        companyName: demoForm.companyName,
        businessEmail: demoForm.email,
        numberOfPropertiesManaged: demoForm.numberOfProperties?.label || '',
      });
      await demoSendRequest;
      setDemoSuccess(true);
    } catch (e) {
      setDemoError('Failed to send request. Please try again.');
    } finally {
      setDemoLoading(false);
    }
  };

  const isManagingProfessionalSelected =
    selectedRole === UserPropertyRelationType.ManagingProfessional;

  const renderContent = () => {
    switch (currentStep) {
      case 0:
        return <OnboardingSteps onRoleSelected={handleRoleSelected} selectedRole={selectedRole} />;
      case 1:
        return isManagingProfessionalSelected && !managerSingleProperty ? (
          <OnboardingDemo
            form={demoForm}
            onSubmit={handleDemoSubmit}
            loading={demoLoading}
            setSelectedRole={setSelectedRole}
            setManagerSingleProperty={setManagerSingleProperty}
            success={demoSuccess}
            error={demoError}
          />
        ) : (
          <OnboardingSteps
            onAddressSelected={handleAddressSelected}
            selectedAddress={selectedAddress || manualAddressData}
            step={1}
            selectedRole={selectedRole}
            forceDropdownPosition="bottom"
          />
        );
      case 2:
        return (
          <OnboardingSteps
            step={2}
            selectedRole={selectedRole}
            selectedPropertyType={selectedPropertyType}
            onPropertyTypeSelected={setSelectedPropertyType}
            selectedOwnershipType={selectedOwnershipType}
            onOwnershipTypeSelected={setSelectedOwnershipType}
          />
        );
      case 3:
        return <OnboardingSteps step={3} />;
      default:
        return <div>{currentStep + 1}</div>;
    }
  };

  const isNextButtonDisabled = () => {
    switch (currentStep) {
      case 0:
        return selectedRole === null;
      case 1:
        if (isManagingProfessionalSelected) return false;
        if (!selectedAddress) return true;
        return typeof selectedAddress === 'string'
          ? selectedAddress.trim() === ''
          : Object.values(selectedAddress).every((v) => !v || v.trim() === '');
      case 2:
        if (selectedRole === UserPropertyRelationType.Tenant || isManagingProfessionalSelected) {
          return !selectedPropertyType;
        }
        return !selectedPropertyType || !selectedOwnershipType;
      default:
        return false;
    }
  };

  const isManagerDemo =
    isManagingProfessionalSelected && currentStep === 1 && !managerSingleProperty;

  return (
    <div className={styles.onboardingWrapper}>
      <OnboardingHeader
        title={isManagerDemo ? 'Request a demo' : 'Set up your account'}
        onBack={
          currentStep > 0 && currentStep < steps.length - 1 && !demoSuccess ? handleBack : undefined
        }
      />
      {!isManagerDemo && (
        <div className={styles.stepperContainer}>
          <OnboardingStepper
            steps={steps.map((s, i) => ({
              slug: s.key,
              isCompleted: i < currentStep,
              isActive: i === currentStep,
            }))}
            onBack={currentStep > 0 ? handleBack : undefined}
          />
        </div>
      )}
      <div className={styles.contentWrapper}>
        <div className={styles.content}>{renderContent()}</div>
      </div>
      {currentStep !== steps.length - 1 && !isManagerDemo && (
        <div className={styles.buttonContainer}>
          <Button
            onClick={handleNext}
            type={ButtonType.PRIMARY}
            color={ButtonColor.GREEN_PRIMARY}
            size={ButtonSize.L}
            className={styles.button}
            state={isNextButtonDisabled() ? ButtonState.DISABLED : ButtonState.DEFAULT}
          >
            {currentStep === 2 ? 'Submit' : 'Next'}
          </Button>
        </div>
      )}
    </div>
  );
};
