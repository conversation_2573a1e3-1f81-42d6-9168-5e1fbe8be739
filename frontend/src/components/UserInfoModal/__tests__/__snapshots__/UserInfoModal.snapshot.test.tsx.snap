// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`UserInfoModal Snapshots > matches snapshot in inline mode 1`] = `
<div>
  <div
    class="_modal_container"
    data-testid="mock-modal"
  >
    <div
      class="_modal_header"
    >
      <h2
        class="_title_3da5cd"
      >
        Your details
      </h2>
      <button
        class="_close_button"
      >
        Close
      </button>
    </div>
    <div
      class="_modal_content"
    >
      <div
        class="_PersonalizationCard_d093b2"
      >
        <div
          class="_personalization_card"
          data-testid="personalization-card"
        >
          <div
            class="_fields_container"
          >
            <div
              class="_field_item"
              data-testid="field-firstName"
            >
              <div
                class="_field_label"
              >
                First name
              </div>
              <div
                class="_field_value"
              >
                John
              </div>
            </div>
            <div
              class="_field_item"
              data-testid="field-lastName"
            >
              <div
                class="_field_label"
              >
                Last name
              </div>
              <div
                class="_field_value"
              >
                Doe
              </div>
            </div>
            <div
              class="_field_item"
              data-testid="field-email"
            >
              <div
                class="_field_label"
              >
                Email address
              </div>
              <div
                class="_field_value"
              >
                <EMAIL>
              </div>
            </div>
            <div
              class="_field_item"
              data-testid="field-phone"
            >
              <div
                class="_field_label"
              >
                Phone number
              </div>
              <div
                class="_field_value"
              >
                +44123456789
              </div>
            </div>
            <div
              class="_field_item"
              data-testid="field-address"
            >
              <div
                class="_field_label"
              >
                Address
              </div>
              <div
                class="_field_value"
              />
            </div>
            <div
              class="_field_item"
              data-testid="field-additionalNotes"
            >
              <div
                class="_field_label"
              >
                Additional notes (optional)
              </div>
              <div
                class="_field_value"
              />
            </div>
          </div>
        </div>
      </div>
      <div
        class="_continueButton_d093b2"
      >
        <button
          class="_button_f8f296 _primary_f8f296 _green-primary_f8f296 _l_f8f296"
        >
          Continue to chat
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`UserInfoModal Snapshots > matches snapshot in loading state 1`] = `
<div>
  <div
    class="_modal_container"
    data-testid="mock-modal"
  >
    <div
      class="_modal_header"
    >
      <h2
        class="_title_3da5cd"
      >
        Your details
      </h2>
      <button
        class="_close_button"
      >
        Close
      </button>
    </div>
    <div
      class="_modal_content"
    >
      <div>
        Loading...
      </div>
    </div>
  </div>
</div>
`;

exports[`UserInfoModal Snapshots > matches snapshot in read-only mode 1`] = `
<div>
  <div
    class="_modal_container"
    data-testid="mock-modal"
  >
    <div
      class="_modal_header"
    >
      <h2
        class="_title_3da5cd"
      >
        Your details
      </h2>
      <button
        class="_close_button"
      >
        Close
      </button>
    </div>
    <div
      class="_modal_content"
    >
      <div
        class="_PersonalizationCard_d093b2"
      >
        <div
          class="_personalization_card"
          data-testid="personalization-card"
        >
          <div
            class="_fields_container"
          >
            <div
              class="_field_item"
              data-testid="field-firstName"
            >
              <div
                class="_field_label"
              >
                First name
              </div>
              <div
                class="_field_value"
              >
                John
              </div>
            </div>
            <div
              class="_field_item"
              data-testid="field-lastName"
            >
              <div
                class="_field_label"
              >
                Last name
              </div>
              <div
                class="_field_value"
              >
                Doe
              </div>
            </div>
            <div
              class="_field_item"
              data-testid="field-email"
            >
              <div
                class="_field_label"
              >
                Email address
              </div>
              <div
                class="_field_value"
              >
                <EMAIL>
              </div>
            </div>
            <div
              class="_field_item"
              data-testid="field-phone"
            >
              <div
                class="_field_label"
              >
                Phone number
              </div>
              <div
                class="_field_value"
              >
                +44123456789
              </div>
            </div>
            <div
              class="_field_item"
              data-testid="field-address"
            >
              <div
                class="_field_label"
              >
                Address
              </div>
              <div
                class="_field_value"
              />
            </div>
            <div
              class="_field_item"
              data-testid="field-additionalNotes"
            >
              <div
                class="_field_label"
              >
                Additional notes (optional)
              </div>
              <div
                class="_field_value"
              />
            </div>
          </div>
        </div>
      </div>
      <div
        class="_continueButton_d093b2"
      >
        <button
          class="_button_f8f296 _primary_f8f296 _green-primary_f8f296 _l_f8f296"
        >
          Continue to chat
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`UserInfoModal Snapshots > matches snapshot when closed 1`] = `<div />`;

exports[`UserInfoModal Snapshots > matches snapshot when open 1`] = `
<div>
  <div
    class="_modal_container"
    data-testid="mock-modal"
  >
    <div
      class="_modal_header"
    >
      <h2
        class="_title_3da5cd"
      >
        Your details
      </h2>
      <button
        class="_close_button"
      >
        Close
      </button>
    </div>
    <div
      class="_modal_content"
    >
      <div
        class="_PersonalizationCard_d093b2"
      >
        <div
          class="_personalization_card"
          data-testid="personalization-card"
        >
          <div
            class="_fields_container"
          >
            <div
              class="_field_item"
              data-testid="field-firstName"
            >
              <div
                class="_field_label"
              >
                First name
              </div>
              <div
                class="_field_value"
              >
                John
              </div>
            </div>
            <div
              class="_field_item"
              data-testid="field-lastName"
            >
              <div
                class="_field_label"
              >
                Last name
              </div>
              <div
                class="_field_value"
              >
                Doe
              </div>
            </div>
            <div
              class="_field_item"
              data-testid="field-email"
            >
              <div
                class="_field_label"
              >
                Email address
              </div>
              <div
                class="_field_value"
              >
                <EMAIL>
              </div>
            </div>
            <div
              class="_field_item"
              data-testid="field-phone"
            >
              <div
                class="_field_label"
              >
                Phone number
              </div>
              <div
                class="_field_value"
              >
                +44123456789
              </div>
            </div>
            <div
              class="_field_item"
              data-testid="field-address"
            >
              <div
                class="_field_label"
              >
                Address
              </div>
              <div
                class="_field_value"
              />
            </div>
            <div
              class="_field_item"
              data-testid="field-additionalNotes"
            >
              <div
                class="_field_label"
              >
                Additional notes (optional)
              </div>
              <div
                class="_field_value"
              />
            </div>
          </div>
        </div>
      </div>
      <div
        class="_continueButton_d093b2"
      >
        <button
          class="_button_f8f296 _primary_f8f296 _green-primary_f8f296 _l_f8f296"
        >
          Continue to chat
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`UserInfoModal Snapshots > matches snapshot with guest user data 1`] = `
<div>
  <div
    class="_modal_container"
    data-testid="mock-modal"
  >
    <div
      class="_modal_header"
    >
      <h2
        class="_title_3da5cd"
      >
        Your details
      </h2>
      <button
        class="_close_button"
      >
        Close
      </button>
    </div>
    <div
      class="_modal_content"
    >
      <div
        class="_PersonalizationCard_d093b2"
      >
        <div
          class="_personalization_card"
          data-testid="personalization-card"
        >
          <div
            class="_fields_container"
          >
            <div
              class="_field_item"
              data-testid="field-firstName"
            >
              <div
                class="_field_label"
              >
                First name
              </div>
              <div
                class="_field_value"
              >
                John
              </div>
            </div>
            <div
              class="_field_item"
              data-testid="field-lastName"
            >
              <div
                class="_field_label"
              >
                Last name
              </div>
              <div
                class="_field_value"
              >
                Doe
              </div>
            </div>
            <div
              class="_field_item"
              data-testid="field-email"
            >
              <div
                class="_field_label"
              >
                Email address
              </div>
              <div
                class="_field_value"
              >
                <EMAIL>
              </div>
            </div>
            <div
              class="_field_item"
              data-testid="field-phone"
            >
              <div
                class="_field_label"
              >
                Phone number
              </div>
              <div
                class="_field_value"
              >
                +44123456789
              </div>
            </div>
            <div
              class="_field_item"
              data-testid="field-address"
            >
              <div
                class="_field_label"
              >
                Address
              </div>
              <div
                class="_field_value"
              />
            </div>
            <div
              class="_field_item"
              data-testid="field-additionalNotes"
            >
              <div
                class="_field_label"
              >
                Additional notes (optional)
              </div>
              <div
                class="_field_value"
              />
            </div>
          </div>
        </div>
      </div>
      <div
        class="_continueButton_d093b2"
      >
        <button
          class="_button_f8f296 _primary_f8f296 _green-primary_f8f296 _l_f8f296"
        >
          Continue to chat
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`UserInfoModal Snapshots > matches snapshot with minimal user data 1`] = `
<div>
  <div
    class="_modal_container"
    data-testid="mock-modal"
  >
    <div
      class="_modal_header"
    >
      <h2
        class="_title_3da5cd"
      >
        Your details
      </h2>
      <button
        class="_close_button"
      >
        Close
      </button>
    </div>
    <div
      class="_modal_content"
    >
      <div
        class="_PersonalizationCard_d093b2"
      >
        <div
          class="_personalization_card"
          data-testid="personalization-card"
        >
          <div
            class="_fields_container"
          >
            <div
              class="_field_item"
              data-testid="field-firstName"
            >
              <div
                class="_field_label"
              >
                First name
              </div>
              <div
                class="_field_value"
              />
            </div>
            <div
              class="_field_item"
              data-testid="field-lastName"
            >
              <div
                class="_field_label"
              >
                Last name
              </div>
              <div
                class="_field_value"
              />
            </div>
            <div
              class="_field_item"
              data-testid="field-email"
            >
              <div
                class="_field_label"
              >
                Email address
              </div>
              <div
                class="_field_value"
              />
            </div>
            <div
              class="_field_item"
              data-testid="field-phone"
            >
              <div
                class="_field_label"
              >
                Phone number
              </div>
              <div
                class="_field_value"
              />
            </div>
            <div
              class="_field_item"
              data-testid="field-address"
            >
              <div
                class="_field_label"
              >
                Address
              </div>
              <div
                class="_field_value"
              />
            </div>
            <div
              class="_field_item"
              data-testid="field-additionalNotes"
            >
              <div
                class="_field_label"
              >
                Additional notes (optional)
              </div>
              <div
                class="_field_value"
              />
            </div>
          </div>
        </div>
      </div>
      <div
        class="_continueButton_d093b2"
      >
        <button
          class="_button_f8f296 _primary_f8f296 _green-primary_f8f296 _l_f8f296"
        >
          Continue to chat
        </button>
      </div>
    </div>
  </div>
</div>
`;
