import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import { UserInfoModal } from '../UserInfoModal';

// Mock hooks
vi.mock('@/hooks/useWidgets', () => ({
  useWidgets: () => ({
    widgets: [],
    isLoading: false,
    fetchProperties: vi.fn(),
    addressValue: '',
    accessInstruction: '',
  }),
}));

vi.mock('@/hooks/useJobSubmissionValidation', () => ({
  default: () => ({
    areRequiredFieldsFilled: true,
  }),
}));

const mockUseUser = vi.hoisted(() => vi.fn());
const mockUseAuth = vi.hoisted(() => vi.fn());
const mockUseClerk = vi.hoisted(() => vi.fn());

vi.mock('@clerk/nextjs', () => ({
  useAuth: mockUseAuth,
  useUser: mockUseUser,
  useClerk: mockUseClerk,
}));

vi.mock('@/components/Modal', () => ({
  Modal: ({ children, open, onClose, title }: any) =>
    open ? (
      <div data-testid="mock-modal">
        <div data-testid="modal-title">{title}</div>
        {children}
        <button onClick={onClose} data-testid="close-button">
          Close
        </button>
      </div>
    ) : null,
}));

// Mock PersonalizationCard
vi.mock('@/components/PersonalizationCard', () => ({
  PersonalizationCard: ({ fields, onEdit, onSave, onCancel, isEditing }: any) => (
    <div data-testid="personalization-card">
      {fields.map((field: any) => (
        <div key={field.id} data-testid={`field-${field.id}`}>
          <span data-testid={`field-label-${field.id}`}>{field.label}</span>
          {field.type === 'component' ? (
            <div>
              {field.component}
              {field.showEditButton && (
                <button onClick={field.onEditClick} data-testid={`edit-${field.id}`}>
                  Edit
                </button>
              )}
            </div>
          ) : (
            <span data-testid={`field-value-${field.id}`}>{field.value}</span>
          )}
        </div>
      ))}
      {isEditing && (
        <div>
          <button onClick={onSave} data-testid="save-button">
            Save
          </button>
          <button onClick={onCancel} data-testid="cancel-button">
            Cancel
          </button>
        </div>
      )}
    </div>
  ),
}));

vi.mock('@/components/Button', () => ({
  Button: ({ children, onClick, state, disabled }: any) => (
    <button onClick={onClick} disabled={disabled || state === 'DISABLED'} data-testid="mock-button">
      {children}
    </button>
  ),
  convertIconToPaths: vi.fn().mockReturnValue([]),
}));

vi.mock('../components/FirstNameField', () => ({
  FirstNameField: ({ initialValue }: any) => (
    <div data-testid="first-name-field">
      <span data-testid="first-name-value">{initialValue || '—'}</span>
    </div>
  ),
}));

vi.mock('../components/LastNameField', () => ({
  LastNameField: ({ initialValue }: any) => (
    <div data-testid="last-name-field">
      <span data-testid="last-name-value">{initialValue || '—'}</span>
    </div>
  ),
}));

describe('UserInfoModal Component', () => {
  let defaultProps: any;

  beforeEach(() => {
    vi.clearAllMocks();

    defaultProps = {
      open: true,
      onClose: vi.fn(),
      readOnly: false,
      inline: false,
    };

    mockUseAuth.mockReturnValue({
      isSignedIn: true,
      getToken: vi.fn().mockResolvedValue('mock-token'),
    });

    mockUseUser.mockReturnValue({
      user: {
        firstName: 'John',
        lastName: 'Doe',
        primaryEmailAddress: {
          emailAddress: '<EMAIL>',
          verification: { status: 'verified' },
        },
        primaryPhoneNumber: {
          phoneNumber: '+44123456789',
          verification: { status: 'verified' },
        },
        phoneNumbers: [{ phoneNumber: '+44123456789' }],
        update: vi.fn().mockResolvedValue({}),
      },
      isLoaded: true,
    });

    mockUseClerk.mockReturnValue({
      user: {
        firstName: 'John',
        lastName: 'Doe',
        primaryEmailAddress: { emailAddress: '<EMAIL>' },
        primaryPhoneNumber: { phoneNumber: '+44123456789' },
      },
    });
  });

  describe('Rendering', () => {
    it('renders when open is true', () => {
      render(<UserInfoModal {...defaultProps} />);
      expect(screen.getByTestId('mock-modal')).toBeInTheDocument();
      expect(screen.getByTestId('modal-title')).toHaveTextContent('Your details');
    });

    it('does not render when open is false', () => {
      render(<UserInfoModal {...defaultProps} open={false} />);
      expect(screen.queryByTestId('mock-modal')).not.toBeInTheDocument();
    });

    it('renders PersonalizationCard with user fields', () => {
      render(<UserInfoModal {...defaultProps} />);
      expect(screen.getByTestId('personalization-card')).toBeInTheDocument();
    });

    it('renders Continue to chat button', () => {
      render(<UserInfoModal {...defaultProps} />);
      expect(screen.getByText('Continue to chat')).toBeInTheDocument();
    });
  });

  describe('Field Rendering', () => {
    it('renders first name field', () => {
      render(<UserInfoModal {...defaultProps} />);
      expect(screen.getByTestId('first-name-field')).toBeInTheDocument();
      expect(screen.getByTestId('first-name-value')).toBeInTheDocument();
    });

    it('renders last name field', () => {
      render(<UserInfoModal {...defaultProps} />);
      expect(screen.getByTestId('last-name-field')).toBeInTheDocument();
      expect(screen.getByTestId('last-name-value')).toBeInTheDocument();
    });

    it('renders edit buttons for fields', () => {
      render(<UserInfoModal {...defaultProps} />);
      expect(screen.getByTestId('edit-firstName')).toBeInTheDocument();
      expect(screen.getByTestId('edit-lastName')).toBeInTheDocument();
    });

    it('displays user data in fields', () => {
      render(<UserInfoModal {...defaultProps} />);
      expect(screen.getByText('John')).toBeInTheDocument();
      expect(screen.getByText('Doe')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('+44123456789')).toBeInTheDocument();
    });
  });

  describe('Modal Interactions', () => {
    it('calls onClose when close button is clicked', async () => {
      const user = userEvent.setup();
      render(<UserInfoModal {...defaultProps} />);

      const closeButton = screen.getByTestId('close-button');
      expect(closeButton).toBeInTheDocument();

      await user.click(closeButton);

      expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
    });

    it('calls onClose when Continue to chat is clicked', async () => {
      const user = userEvent.setup();
      render(<UserInfoModal {...defaultProps} />);

      const continueButton = screen.getByText('Continue to chat');
      await user.click(continueButton);

      expect(defaultProps.onClose).toHaveBeenCalled();
    });
  });

  describe('Read-only Mode', () => {
    it('renders in read-only mode when readOnly is true', () => {
      render(<UserInfoModal {...defaultProps} readOnly={true} />);

      expect(screen.getByTestId('mock-modal')).toBeInTheDocument();
    });
  });

  describe('Loading State', () => {
    it('shows loading state when user is not loaded', () => {
      mockUseUser.mockReturnValue({
        user: null,
        isLoaded: false,
      });

      render(<UserInfoModal {...defaultProps} />);

      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });
  });

  describe('Guest User Handling', () => {
    it('handles guest user conversion', () => {
      render(<UserInfoModal {...defaultProps} />);

      expect(screen.getByTestId('mock-modal')).toBeInTheDocument();
    });
  });

  describe('Data Updates', () => {
    it('displays user data correctly', () => {
      render(<UserInfoModal {...defaultProps} />);
      expect(screen.getByTestId('first-name-value')).toHaveTextContent('John');
    });

    it('updates display when user data changes', () => {
      const { rerender } = render(<UserInfoModal {...defaultProps} />);

      expect(screen.getByTestId('first-name-value')).toHaveTextContent('John');

      mockUseUser.mockReturnValue({
        user: {
          firstName: 'UpdatedJohn',
          lastName: 'UpdatedDoe',
          primaryEmailAddress: { emailAddress: '<EMAIL>' },
          primaryPhoneNumber: { phoneNumber: '+44987654321' },
          phoneNumbers: [{ phoneNumber: '+44987654321' }],
          update: vi.fn().mockResolvedValue({}),
        },
        isLoaded: true,
      });

      rerender(<UserInfoModal {...defaultProps} />);

      expect(screen.getByTestId('first-name-value')).toHaveTextContent('UpdatedJohn');
    });
  });

  describe('Component Integration', () => {
    it('renders all required components', () => {
      render(<UserInfoModal {...defaultProps} />);

      expect(screen.getByTestId('personalization-card')).toBeInTheDocument();
      expect(screen.getByTestId('first-name-field')).toBeInTheDocument();
      expect(screen.getByTestId('last-name-field')).toBeInTheDocument();
      expect(screen.getByText('Continue to chat')).toBeInTheDocument();
    });
  });
});
