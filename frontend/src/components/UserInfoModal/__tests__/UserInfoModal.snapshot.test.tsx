import React from 'react';
import { render } from '@testing-library/react';
import { vi } from 'vitest';
import { UserInfoModal } from '../UserInfoModal';

// Mock hooks
vi.mock('@/hooks/useWidgets', () => ({
  useWidgets: () => ({
    widgets: [],
    isLoading: false,
    fetchProperties: vi.fn(),
    addressValue: '',
    accessInstruction: '',
  }),
}));

vi.mock('@/hooks/useJobSubmissionValidation', () => ({
  default: () => ({
    areRequiredFieldsFilled: true,
  }),
}));

const mockUseUser = vi.hoisted(() => vi.fn());
const mockUseAuth = vi.hoisted(() => vi.fn());
const mockUseClerk = vi.hoisted(() => vi.fn());

vi.mock('@clerk/nextjs', () => ({
  useAuth: mockUseAuth,
  useUser: mockUseUser,
  useClerk: mockUseClerk,
}));

vi.mock('@/components/Modal', () => ({
  Modal: ({ children, open, onClose, title }: any) =>
    open ? (
      <div data-testid="mock-modal" className="_modal_container">
        <div className="_modal_header">
          <h2 className="_title_3da5cd">{title}</h2>
          <button onClick={onClose} className="_close_button">
            Close
          </button>
        </div>
        <div className="_modal_content">{children}</div>
      </div>
    ) : null,
}));

// Mock PersonalizationCard
vi.mock('@/components/PersonalizationCard', () => ({
  PersonalizationCard: ({ fields, onEdit, onSave, onCancel, isEditing, title }: any) => (
    <div data-testid="personalization-card" className="_personalization_card">
      {title && <h3 className="_card_title">{title}</h3>}
      <div className="_fields_container">
        {fields.map((field: any) => (
          <div key={field.id} className="_field_item" data-testid={`field-${field.id}`}>
            <div className="_field_label">{field.label}</div>
            <div className="_field_value">{field.value}</div>
            {field.editable && !isEditing && (
              <button onClick={() => onEdit(field.id)} className="_edit_button">
                Edit
              </button>
            )}
          </div>
        ))}
      </div>
      {isEditing && (
        <div className="_editing_controls">
          <button onClick={onSave} className="_save_button">
            Save
          </button>
          <button onClick={onCancel} className="_cancel_button">
            Cancel
          </button>
        </div>
      )}
    </div>
  ),
}));

vi.mock('@/components/Button', () => ({
  Button: ({ children, onClick, state, disabled, type, color, size }: any) => (
    <button
      onClick={onClick}
      disabled={disabled || state === 'DISABLED'}
      className={`_button_f8f296 _${type || 'primary'}_f8f296 _${color || 'green-primary'}_f8f296 _${size || 'l'}_f8f296`}
    >
      {children}
    </button>
  ),
  convertIconToPaths: vi.fn().mockReturnValue([]),
}));

vi.mock('../components/FirstNameField', () => ({
  FirstNameField: ({ initialValue, onSave, isEditing, onEditStart, onEditEnd }: any) => (
    <div className="_field_component _first_name_field">
      <div className="_field_display">
        <span className="_field_value">{initialValue}</span>
        {!isEditing && (
          <button onClick={() => onEditStart('firstName')} className="_edit_button">
            <svg className="_icon" width="16" height="16" viewBox="0 0 24 24">
              <path
                d="M16.2141 4.98239L17.6158 3.58063C18.39 2.80646 19.6452 2.80646 20.4194 3.58063C21.1935 4.3548 21.1935 5.61001 20.4194 6.38418L19.0176 7.78594M16.2141 4.98239L10.9802 10.2163C9.93493 11.2616 9.41226 11.7842 9.05637 12.4211C8.70047 13.058 8.3424 14.5619 8 16C9.43809 15.6576 10.942 15.2995 11.5789 14.9436C12.2158 14.5877 12.7384 14.0651 13.7837 13.0198L19.0176 7.78594M16.2141 4.98239L19.0176 7.78594"
                stroke="currentColor"
                strokeWidth="1.5"
              />
            </svg>
          </button>
        )}
      </div>
      {isEditing && (
        <div className="_field_editing">
          <input className="_field_input" defaultValue={initialValue} />
          <div className="_editing_buttons">
            <button onClick={() => onSave('NewFirstName')} className="_save_button">
              Save
            </button>
            <button onClick={() => onEditEnd('firstName')} className="_cancel_button">
              Cancel
            </button>
          </div>
        </div>
      )}
    </div>
  ),
}));

vi.mock('../components/LastNameField', () => ({
  LastNameField: ({ initialValue, onSave, isEditing, onEditStart, onEditEnd }: any) => (
    <div className="_field_component _last_name_field">
      <div className="_field_display">
        <span className="_field_value">{initialValue}</span>
        {!isEditing && (
          <button onClick={() => onEditStart('lastName')} className="_edit_button">
            <svg className="_icon" width="16" height="16" viewBox="0 0 24 24">
              <path
                d="M16.2141 4.98239L17.6158 3.58063C18.39 2.80646 19.6452 2.80646 20.4194 3.58063C21.1935 4.3548 21.1935 5.61001 20.4194 6.38418L19.0176 7.78594M16.2141 4.98239L10.9802 10.2163C9.93493 11.2616 9.41226 11.7842 9.05637 12.4211C8.70047 13.058 8.3424 14.5619 8 16C9.43809 15.6576 10.942 15.2995 11.5789 14.9436C12.2158 14.5877 12.7384 14.0651 13.7837 13.0198L19.0176 7.78594M16.2141 4.98239L19.0176 7.78594"
                stroke="currentColor"
                strokeWidth="1.5"
              />
            </svg>
          </button>
        )}
      </div>
      {isEditing && (
        <div className="_field_editing">
          <input className="_field_input" defaultValue={initialValue} />
          <div className="_editing_buttons">
            <button onClick={() => onSave('NewLastName')} className="_save_button">
              Save
            </button>
            <button onClick={() => onEditEnd('lastName')} className="_cancel_button">
              Cancel
            </button>
          </div>
        </div>
      )}
    </div>
  ),
}));

vi.mock('../components/PhoneField', () => ({
  PhoneField: () => <div className="_field_component _phone_field">Phone Field</div>,
}));

vi.mock('../components/EmailField', () => ({
  EmailField: () => <div className="_field_component _email_field">Email Field</div>,
}));

vi.mock('../components/AddressField', () => ({
  AddressField: () => <div className="_field_component _address_field">Address Field</div>,
}));

vi.mock('../components/AccessInstructionField', () => ({
  AccessInstructionField: () => <div className="_field_component _access_field">Access Field</div>,
}));

describe('UserInfoModal Snapshots', () => {
  const defaultProps = {
    open: true,
    onClose: vi.fn(),
    readOnly: false,
    inline: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();

    mockUseAuth.mockReturnValue({
      isSignedIn: true,
      getToken: vi.fn().mockResolvedValue('mock-token'),
    });

    mockUseUser.mockReturnValue({
      user: {
        firstName: 'John',
        lastName: 'Doe',
        primaryEmailAddress: {
          emailAddress: '<EMAIL>',
          verification: { status: 'verified' },
        },
        primaryPhoneNumber: {
          phoneNumber: '+44123456789',
          verification: { status: 'verified' },
        },
        phoneNumbers: [{ phoneNumber: '+44123456789' }],
        update: vi.fn().mockResolvedValue({}),
      },
      isLoaded: true,
    });

    mockUseClerk.mockReturnValue({
      user: {
        firstName: 'John',
        lastName: 'Doe',
        primaryEmailAddress: { emailAddress: '<EMAIL>' },
        primaryPhoneNumber: { phoneNumber: '+44123456789' },
      },
    });
  });

  it('matches snapshot when open', () => {
    const { container } = render(<UserInfoModal {...defaultProps} />);
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot when closed', () => {
    const { container } = render(<UserInfoModal {...defaultProps} open={false} />);
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot in read-only mode', () => {
    const { container } = render(<UserInfoModal {...defaultProps} readOnly={true} />);
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot in inline mode', () => {
    const { container } = render(<UserInfoModal {...defaultProps} inline={true} />);
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with guest user data', () => {
    const { container } = render(<UserInfoModal {...defaultProps} />);
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot in loading state', () => {
    mockUseUser.mockReturnValue({
      user: null,
      isLoaded: false,
    });

    const { container } = render(<UserInfoModal {...defaultProps} />);
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with minimal user data', () => {
    mockUseUser.mockReturnValue({
      user: {
        firstName: '',
        lastName: '',
        primaryEmailAddress: null,
        primaryPhoneNumber: null,
        phoneNumbers: [],
        update: vi.fn().mockResolvedValue({}),
      },
      isLoaded: true,
    });

    const { container } = render(<UserInfoModal {...defaultProps} />);
    expect(container).toMatchSnapshot();
  });
});
