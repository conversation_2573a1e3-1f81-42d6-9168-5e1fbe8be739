import React from 'react';
import { Modal } from '@/components/Modal/Modal';
import { Button } from '@/components/Button';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import { WelcomeAssistantModalProps } from './WelcomeAssistantModal.types';
import { CheckmarkCircleIcon } from '@hugeicons-pro/core-stroke-standard';
import { IconSvgObject } from '@hugeicons/react';
import { ButtonType, ButtonColor, ButtonSize } from '@/components/Button/Button.types';
import styles from './WelcomeAssistantModal.module.scss';
import { useModalForGuest } from '@/hooks/useModalGuest';
import { SignInButton, SignUpButton } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';

const features = [
  'Get personalised home assistance & property advice',
  'Have AI build your property profile for you',
  'Create to-do lists and receive smart suggestions',
  'Chat with <PERSON><PERSON> without limits',
];

export const WelcomeAssistantModal: React.FC<
  Omit<WelcomeAssistantModalProps, 'open' | 'onClose'>
> = () => {
  const { isModalOpen, closeModal } = useModalForGuest();
  const router = useRouter();

  return (
    <Modal
      open={isModalOpen}
      onClose={closeModal}
      closeButtonPosition="right"
      actionButtons={
        <div style={{ maxWidth: '338px', width: '100%', margin: '0 auto' }}>
          <div className={styles.actionButtonWrapper}>
            <SignUpButton mode="modal">
              <Button
                type={ButtonType.PRIMARY}
                color={ButtonColor.GREEN_PRIMARY}
                size={ButtonSize.L}
                onClick={closeModal}
                className={styles.signUpButton}
              >
                Sign up
              </Button>
            </SignUpButton>
          </div>
          <div className={styles.loginWrapper}>
            Already have an account? <SignInButton mode="modal"><strong onClick={closeModal}>Log in</strong></SignInButton>
          </div>
        </div>
      }
    >
      <div style={{ maxWidth: '338px', width: '100%', margin: '0 auto' }}>
        <div className={styles.container}>
          <img src="/brainHome.png" alt="Personal Assistant" className={styles.image} />
          <h2 className={styles.title}>Unlock Your Personalised Home Assistant</h2>
          <ul className={styles.featuresList}>
            {features.map((f, i) => (
              <li className={styles.featureItem}>
                <span className={styles.featureIcon}>
                  <HugeiconsIcon
                    icon={CheckmarkCircleIcon as unknown as IconSvgObject}
                    size={24}
                  />
                </span>
                <span>{f}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </Modal>
  );
};
