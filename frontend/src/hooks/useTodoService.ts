import {
  acceptTodo,
  deleteTodo,
  fetchTodoDetails,
  fetchTodos,
  updateTodo,
} from '@/api/todos/todos';
import { ITodoDto, IUpdateTodoDto } from '@/api/todos/types';
import { API_ENDPOINTS, getApiUrl } from '@/constants/api';
import { useAuth } from '@clerk/nextjs';
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';

export const useGetTodoDetails = (todoId: ITodoDto['id'] | null) => {
  const { getToken } = useAuth();

  const fetchTodoDetailsWithGetToken = async (url: string) => {
    const freshToken = await getToken();

    return fetchTodoDetails(url, freshToken!);
  };

  return useSWR(
    todoId ? getApiUrl(`${API_ENDPOINTS.TODOS}/${todoId}`) : null,
    fetchTodoDetailsWithGetToken
  );
};

export const useUpdateTodo = () => {
  const { getToken } = useAuth();

  const updateTodoWithGetToken = async (
    _key: string,
    { arg }: { arg: { todoId: ITodoDto['id']; data: IUpdateTodoDto } }
  ) => {
    const token = await getToken();
    const url = getApiUrl(`${API_ENDPOINTS.TODOS}/${arg.todoId}`);

    return updateTodo(url, { arg: { token: token!, updateTodo: arg.data } });
  };

  return useSWRMutation('updateTodo', updateTodoWithGetToken);
};

export const useGetTodos = (enabled = true) => {
  const { getToken } = useAuth();

  const fetchTodosWithGetToken = async (url: string) => {
    const freshToken = await getToken();

    return fetchTodos(url, freshToken!);
  };

  return useSWR(
    enabled ? `${getApiUrl(API_ENDPOINTS.TODOS_ACCEPTED)}` : null,
    fetchTodosWithGetToken
  );
};

export const useGetTodosSuggested = (enabled = true) => {
  const { getToken } = useAuth();

  const fetchTodosSuggestedWithGetToken = async (url: string) => {
    const freshToken = await getToken();

    return fetchTodos(url, freshToken!);
  };

  return useSWR(
    enabled ? getApiUrl(API_ENDPOINTS.TODOS_SUGGESTED) : null,
    fetchTodosSuggestedWithGetToken
  );
};

export const useDeleteTodo = () => {
  const { getToken } = useAuth();

  const deleteTodoWithGetToken = async (
    _key: string,
    { arg }: { arg: { todoId: ITodoDto['id'] } }
  ) => {
    const token = await getToken();
    const url = getApiUrl(`${API_ENDPOINTS.TODOS}/${arg.todoId}`);

    return deleteTodo(url, { arg: { token: token! } });
  };

  return useSWRMutation('deleteTodo', deleteTodoWithGetToken);
};

export const useAcceptTodo = () => {
  const { getToken } = useAuth();

  const acceptTodoWithGetToken = async (
    _key: string,
    { arg }: { arg: { todoId: ITodoDto['id'] } }
  ) => {
    const token = await getToken();
    const url = getApiUrl(`${API_ENDPOINTS.TODO_ACCEPT.replace('{todoId}', String(arg.todoId))}`);

    return acceptTodo(url, { arg: { token: token! } });
  };

  return useSWRMutation('acceptTodo', acceptTodoWithGetToken);
};

export const useRejectTodo = () => {
  const { getToken } = useAuth();

  const acceptTodoWithGetToken = async (
    _key: string,
    { arg }: { arg: { todoId: ITodoDto['id'] } }
  ) => {
    const token = await getToken();
    const url = getApiUrl(`${API_ENDPOINTS.TODO_REJECT.replace('{todoId}', String(arg.todoId))}`);

    return acceptTodo(url, { arg: { token: token! } });
  };

  return useSWRMutation('acceptTodo', acceptTodoWithGetToken);
};
