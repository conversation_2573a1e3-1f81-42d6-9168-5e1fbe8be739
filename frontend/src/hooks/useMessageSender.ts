import { useAuth } from '@clerk/nextjs';
import { useRouter, useSearchParams } from 'next/navigation';
import useChatParams from '@/hooks/useChatParams';
import { useChats } from './useChats';
import { Attachment } from '@/types/messages';
import { useCallback, useMemo } from 'react';
import { ICreateEntityLinkDto } from '@/api/entityLink';
import { useCreateEntityLink } from './useEntityLinkService';
import { useUniversalAuth } from './useUniversalAuth';

export default function useMessageSender() {
  const { getToken: getAuthToken } = useUniversalAuth();
  const router = useRouter();
  const { chatId } = useChatParams();
  const { sendMessage } = useChats();
  const searchParams = useSearchParams();
  const { trigger: createEntityLink } = useCreateEntityLink();

  const linkedEntity = useMemo(() => {
    const todoId = searchParams?.get('todo');

    if (!todoId || chatId) return undefined;

    return {
      ...(todoId && {
        entityId: Number(todoId),
        entityType: 'todos' as ICreateEntityLinkDto['entityTypeB'],
      }),
    };
  }, [searchParams]);

  const wrappedSendMessage = useCallback(
    async (value: string, attachments?: Attachment[]): Promise<boolean> => {
      try {
        if (!chatId) {
          console.log('useMessageSender router.push(`/chats`)');
          router.replace(`/chats`);
        }
        console.log('useMessageSender getting a token');
        const token = await getAuthToken();
        console.log('useMessageSender token received');

        if (token) {
          console.log('useMessageSender sending a message');
          await sendMessage(chatId, value, token, attachments, (newChatId) => {
            console.log(`useMessageSender router.replace('/chats/${newChatId}')`);

            if (linkedEntity) {
              const linkedEntityWithChatId: ICreateEntityLinkDto = {
                entityIdA: newChatId,
                entityIdB: linkedEntity.entityId!,
                entityTypeA: 'chats',
                entityTypeB: linkedEntity.entityType!,
              };

              createEntityLink({ linkingEntity: linkedEntityWithChatId });
            }

            chatId ? router.replace(`/chats/${chatId}`) : router.replace(`/chats/${newChatId}`);
          });
          return true;
        }
      } catch (error) {
        console.error('Failed to send message:', error);
      }
      return false;
    },
    [chatId, getAuthToken, router, sendMessage]
  );

  return {
    sendMessage: wrappedSendMessage,
  };
}
