import { useState, useRef, useEffect, useCallback } from 'react';

export enum PhoneVerificationState {
  INITIAL = 'initial',
  CODE_SENT = 'codeSent',
  VERIFIED = 'verified',
  ERROR = 'error',
}

interface UseVerificationModalParams {
  onSendCode: (value: string) => Promise<boolean>;
  onVerifyCode: (code: string, value: string) => Promise<boolean>;
  onSave?: (value: string) => void;
  type: string;
  loading: boolean;
  clerk: any;
}

export function useVerificationModal({
  onSendCode,
  onVerifyCode,
  onSave,
  type,
  loading,
  clerk,
}: UseVerificationModalParams) {
  const [verificationState, setVerificationState] = useState<PhoneVerificationState>(
    PhoneVerificationState.INITIAL
  );
  const [verificationCode, setVerificationCode] = useState<string>('');
  const [verificationDigits, setVerificationDigits] = useState<string[]>(['', '', '', '', '', '']);
  const [targetValue, setTargetValue] = useState<string>('');
  const [verificationError, setVerificationError] = useState<string>('');
  const [resendCountdown, setResendCountdown] = useState<number>(0);
  const [isResendDisabled, setIsResendDisabled] = useState<boolean>(false);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const codeInputRefs = useRef<(HTMLInputElement | null)[]>([]);

  useEffect(() => {
    let timer: ReturnType<typeof setTimeout> | undefined;
    if (resendCountdown > 0) {
      timer = setTimeout(() => {
        setResendCountdown((prev) => prev - 1);
      }, 1000);
    } else if (resendCountdown === 0 && isResendDisabled) {
      setIsResendDisabled(false);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [resendCountdown, isResendDisabled]);

  useEffect(() => {
    setVerificationCode(verificationDigits.join(''));
  }, [verificationDigits]);

  const handleSendCode = useCallback(
    async (value: string) => {
      setVerificationError('');
      setTargetValue(value);
      try {
        const result = await onSendCode(value);
        if (result) {
          setVerificationState(PhoneVerificationState.CODE_SENT);
          setIsModalOpen(true);
          setIsResendDisabled(true);
          setResendCountdown(30);
        } else {
          setVerificationError('Failed to send verification code');
        }
      } catch {
        setVerificationError('Error sending verification code');
      }
    },
    [onSendCode]
  );

  const handleVerifyCode = useCallback(async () => {
    setVerificationError('');
    try {
      const result = await onVerifyCode(verificationCode, targetValue);
      if (result) {
        setVerificationState(PhoneVerificationState.VERIFIED);
        setIsModalOpen(false);
        onSave?.(targetValue);
      } else {
        setVerificationError('Invalid verification code');
      }
    } catch {
      setVerificationError('Error verifying code');
    }
  }, [onVerifyCode, verificationCode, targetValue, onSave]);

  const handleCloseModal = useCallback(() => {
    setIsModalOpen(false);
    setVerificationState(PhoneVerificationState.INITIAL);
    setVerificationDigits(['', '', '', '', '', '']);
    setVerificationCode('');
    setVerificationError('');
  }, []);

  const handleDigitChange = (index: number, value: string) => {
    const digit = value.replace(/\D/g, '').substring(0, 1);
    const newDigits = [...verificationDigits];
    newDigits[index] = digit;
    setVerificationDigits(newDigits);
    if (digit && index < 5) {
      codeInputRefs.current[index + 1]?.focus();
    }
  };

  const handleDigitKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    const target = e.target as HTMLInputElement;
    if (/^\d$/.test(e.key)) {
      e.preventDefault();
      if (e.key === verificationDigits[index] && index < 5) {
        codeInputRefs.current[index + 1]?.focus();
        return;
      }
      handleDigitChange(index, e.key);
      return;
    }
    if (e.key === 'Backspace' && !verificationDigits[index] && index > 0) {
      codeInputRefs.current[index - 1]?.focus();
      return;
    }
    if (e.key === 'ArrowLeft' && index > 0) {
      const prevInput = codeInputRefs.current[index - 1];
      prevInput?.focus();
      handleDigitInput(prevInput);
      return;
    }
    if (e.key === 'ArrowRight' && index < 5) {
      const nextInput = codeInputRefs.current[index + 1];
      nextInput?.focus();
      handleDigitInput(nextInput);
      return;
    }
    handleDigitInput(target);
  };

  const handleDigitFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    e.target.select();
  };

  const handleDigitInput = (target: HTMLInputElement | null) => {
    if (!target) return;
    const length = target.value.length;
    setTimeout(() => {
      try {
        if (target.setSelectionRange) {
          target.setSelectionRange(length, length);
        }
      } catch {}
    }, 0);
  };

  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text');
    const digits = pastedData.replace(/\D/g, '').substring(0, 6).split('');
    const newDigits = [...verificationDigits];
    digits.forEach((digit: string, index: number) => {
      if (index < 6) {
        newDigits[index] = digit;
      }
    });
    setVerificationDigits(newDigits);
    const nextEmptyIndex = newDigits.findIndex((d) => !d);
    if (nextEmptyIndex >= 0) {
      codeInputRefs.current[nextEmptyIndex]?.focus();
    } else if (newDigits[5]) {
      codeInputRefs.current[5]?.focus();
    }
  };

  return {
    verificationState,
    verificationCode,
    verificationDigits,
    targetValue,
    verificationError,
    resendCountdown,
    isResendDisabled,
    isModalOpen,
    codeInputRefs,
    setVerificationDigits,
    setVerificationCode,
    setTargetValue,
    setIsModalOpen,
    handleSendCode,
    handleVerifyCode,
    handleCloseModal,
    handleDigitChange,
    handleDigitKeyDown,
    handleDigitFocus,
    handleDigitInput,
    handlePaste,
  };
}
