import { create } from 'zustand';
import { useBreakpoint } from '@/utils/breakpointUtils';
import React from 'react';
import { PropertyTenureType, PropertyType, UserPropertyRelationType } from '@/api/properties';

type Step = { key: string; label: string };
type AddressFields = { line1: string; line2: string; city: string; postcode: string };

interface OnboardingState {
  // Navigation state
  steps: Step[];
  currentStep: number;
  stepCompletion: Record<number, boolean>;

  // Onboarding data
  selectedRole: UserPropertyRelationType | null;
  selectedAddress: string | AddressFields | '';
  addressType: 'string' | 'object' | null;
  selectedPropertyType: PropertyType | null;
  selectedOwnershipType: PropertyTenureType | null;

  // Actions for navigation
  nextStep: () => void;
  prevStep: () => void;
  goToStep: (index: number) => void;
  reset: () => void;
  setStepComplete: (index: number, complete: boolean) => void;

  // Actions for onboarding data
  setSelectedRole: (role: UserPropertyRelationType | null) => void;
  setSelectedAddress: (address: string | AddressFields) => void;
  setSelectedPropertyType: (type: PropertyType | null) => void;
  setSelectedOwnershipType: (type: PropertyTenureType | null) => void;
  clearData: (dataType: 'address' | 'propertyTypes' | 'all') => void;
}

const useOnboardingStore = create<OnboardingState>()((set, get) => ({
  // Navigation state
  steps: [
    { key: 'role', label: 'First page' },
    { key: 'address', label: 'Second page' },
    { key: 'details', label: 'Third page' },
    { key: 'finish', label: 'Fourth page' },
  ],
  currentStep: 0,
  stepCompletion: { 0: false, 1: false, 2: false, 3: false, 4: false },

  // Onboarding data
  selectedRole: null,
  selectedAddress: '',
  addressType: null,
  selectedPropertyType: null,
  selectedOwnershipType: null,

  // Actions for navigation
  nextStep: () => {
    const { currentStep, steps } = get();
    if (currentStep < steps.length - 1) {
      set({ currentStep: currentStep + 1 });
    }
  },
  prevStep: () => {
    const { currentStep } = get();
    if (currentStep > 0) set({ currentStep: currentStep - 1 });
  },
  goToStep: (index) => {
    const { steps } = get();
    if (index >= 0 && index < steps.length) set({ currentStep: index });
  },
  reset: () =>
    set({
      currentStep: 0,
      stepCompletion: { 0: false, 1: false, 2: false, 3: false },
      selectedRole: null,
      selectedAddress: '',
      addressType: null,
      selectedPropertyType: null,
      selectedOwnershipType: null,
    }),
  setStepComplete: (index, complete) =>
    set((state) => ({
      stepCompletion: { ...state.stepCompletion, [index]: complete },
    })),

  // Actions for onboarding data
  setSelectedRole: (role) => {
    set({ selectedRole: role });

    // If role changes, clear related data
    if (role !== get().selectedRole) {
      get().clearData('all');
    }
  },
  setSelectedAddress: (address) => {
    set({
      selectedAddress: address,
      addressType: typeof address === 'string' ? 'string' : 'object',
    });
  },
  setSelectedPropertyType: (type) => set({ selectedPropertyType: type }),
  setSelectedOwnershipType: (type) => set({ selectedOwnershipType: type }),
  clearData: (dataType) => {
    if (dataType === 'address' || dataType === 'all') {
      set({
        selectedAddress: '',
        addressType: null,
      });
    }

    if (dataType === 'propertyTypes' || dataType === 'all') {
      set({
        selectedPropertyType: null,
        selectedOwnershipType: null,
      });
    }
  },
}));

export function useOnboarding() {
  const { isMobile } = useBreakpoint();
  const {
    // Navigation state
    steps: allSteps,
    currentStep,
    stepCompletion,
    nextStep,
    prevStep,
    goToStep,
    reset,
    setStepComplete,

    // Onboarding data
    selectedRole,
    selectedAddress,
    addressType,
    selectedPropertyType,
    selectedOwnershipType,

    // Actions for onboarding data
    setSelectedRole,
    setSelectedAddress,
    setSelectedPropertyType,
    setSelectedOwnershipType,
    clearData,
  } = useOnboardingStore();

  const steps = React.useMemo(() => {
    return isMobile ? allSteps.slice(0, 4) : allSteps;
  }, [isMobile, allSteps]);

  return {
    // Navigation state
    steps,
    currentStep,
    stepCompletion,
    nextStep,
    prevStep,
    goToStep,
    reset,
    setStepComplete,

    // Onboarding data
    selectedRole,
    selectedAddress,
    addressType,
    selectedPropertyType,
    selectedOwnershipType,

    // Actions for onboarding data
    setSelectedRole,
    setSelectedAddress,
    setSelectedPropertyType,
    setSelectedOwnershipType,
    clearData,
  };
}
