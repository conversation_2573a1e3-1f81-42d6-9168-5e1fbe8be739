import {
  createEntityLink,
  getEntityLink,
  ICreateEntityLinkDto,
  IGetEntityLinkDto,
} from '@/api/entityLink';
import { API_ENDPOINTS, getApiUrl } from '@/constants/api';
import { useAuth } from '@clerk/nextjs';
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';

export const useCreateEntityLink = () => {
  const { getToken } = useAuth();

  const createEntityLinkkWithToken = async (
    _key: string,
    { arg }: { arg: { linkingEntity: ICreateEntityLinkDto } }
  ) => {
    const token = await getToken();
    const url = getApiUrl(`${API_ENDPOINTS.ENTITY_LINKS}`);

    return createEntityLink(url, { arg: { token: token!, linkingEntity: arg.linkingEntity } });
  };

  return useSWRMutation('createLink', createEntityLinkkWithToken);
};

export const useGetEntityLink = (linkingEntity: IGetEntityLinkDto) => {
  const { getToken } = useAuth();

  const getEntityLinkWithToken = async (url: string) => {
    const token = await getToken();

    return getEntityLink(url, { arg: { token: token!, linkingEntity } });
  };

  return useSWR(
    linkingEntity.entityId >= 0 ? getApiUrl(`${API_ENDPOINTS.ENTITY_LINKS}`) : null,
    getEntityLinkWithToken
  );
};
